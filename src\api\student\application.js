import Request from '@/request'

/**
 * 学生提交积分申请
 * @param {Object} data 申请数据
 * @returns {Promise}
 */
export function submitStudentApplication(data) {
    console.log('提交学生申请数据:', data);
    
    // 确保数据格式符合后端要求
    const requestData = {
        studentNo: data.studentNo || '', 
        classId: data.classId || 0,
        pointsChange: data.pointsChange || 1, // 1为加分，2为减分
        points: data.points || 0,
        reason: data.reason || '',
        img: data.img || '', // MinIO上传后的图片URL，多个URL用逗号分隔
        status: 1, // 待审核状态
        status1: 1, // 待审核状态
        status2: 1 // 待审核状态
    };

    console.log('格式化后的申请数据:', requestData);

    return Request({
        url: '/points-apply/studentApply',
        method: 'post',
        data: requestData
    })
}

/**
 * 获取学生申请记录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getStudentApplicationList(params = {}) {
    console.log('查询学生申请记录参数:', params);
    
    return Request({
        url: '/points-apply/studentApplicationList',
        method: 'post',
        data: params
    })
}

/**
 * 撤销学生申请
 * @param {Number} applyId 申请ID
 * @returns {Promise}
 */
export function cancelStudentApplication(applyId) {
    console.log('撤销申请ID:', applyId);
    
    return Request({
        url: '/points-apply/cancel',
        method: 'post',
        data: { applyId: applyId }
    })
}

/**
 * 获取学生信息（用于申请时填充学号等信息）
 * @returns {Promise}
 */
export function getStudentInfo() {
    return Request({
        url: '/edu-student/getCurrentStudentInfo',
        method: 'get'
    })
}

/**
 * 上传申请附件
 * @param {File} file 文件对象
 * @returns {Promise}
 */
export function uploadApplicationFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return Request({
        url: '/minio/uploadFile',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// export function mockStudentsLogin(phone, password){
//     return Request({
//         url: '/edu-student/login',
//         method: 'post',
//         data: {
//             phone,
//             password
//         }
//     })
// }

/**
 * 获取学生积分申请记录列表
 * @param {Object} data 查询参数
 * @param {String} data.studentNo 学生学号
 * @param {Number} data.pageNum 页码
 * @param {Number} data.pageSize 每页大小
 * @param {Number} data.pointsChange 积分变动类型 (1-加分, 2-减分)
 * @param {Date} data.startTime 开始时间
 * @param {Date} data.endTime 结束时间
 * @returns {Promise}
 */
export function getPointsApplyList(data = {}){
    console.log('调用积分申请列表API，参数:', data);

    // 确保必要的参数存在
    const params = {
        pageNum: data.pageNum || 1,
        pageSize: data.pageSize || 10,
        studentNo: data.studentNo || '',
        ...data
    };

    return Request({
        url: '/points-apply/studentList',
        method: 'post',
        data: params
    })
}

/**
 * 获取学生参与的活动记录列表
 * @param {Object} data 查询参数
 * @param {String} data.studentNo 学生学号
 * @param {Number} data.pageNum 页码
 * @param {Number} data.pageSize 每页大小
 * @returns {Promise}
 */
export function getActivityStudentList(data = {}){
    console.log('调用学生活动记录API，参数:', data);

    // 确保必要的参数存在
    const params = {
        pageNum: data.pageNum || 1,
        pageSize: data.pageSize || 10,
        studentNo: data.studentNo || '',
        ...data
    };

    return Request({
        url: '/activityApplication/findStudentActivity',
        method: 'post',
        data: params
    })
}